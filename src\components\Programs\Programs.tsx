import React from 'react';
import { ProgramsProps } from '../../types';

const Programs: React.FC<ProgramsProps> = ({ programs }) => {
  return (
    <section id="programs" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Programs Offered</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive health sciences education programs designed to meet industry standards and prepare students for successful careers.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {programs.map((program, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden">
              <div className="bg-gradient-to-r from-blue-600 to-teal-600 p-6">
                <h3 className="text-lg font-bold text-white leading-tight">
                  {program.title}
                </h3>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Duration:</h4>
                  <p className="text-gray-700">{program.duration}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Entry Qualifications:</h4>
                  <p className="text-gray-700">{program.qualifications}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Fee Structure:</h4>
                  <p className="text-gray-700">{program.fees}</p>
                </div>
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors duration-300">
                  Learn More
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Programs;
